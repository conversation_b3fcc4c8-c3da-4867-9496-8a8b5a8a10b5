{"name": "equtum-batch", "version": "1.0.0", "description": "TypeScript + Prisma batch processing system for Equtum", "main": "dist/index.js", "scripts": {"start": "tsx src/index.ts", "prisma:generate": "prisma generate", "lint": "biome lint src/", "lint:fix": "biome lint --apply src/", "format": "biome format --write src/", "check": "biome check src/", "check:fix": "biome check --apply src/", "test": "NODE_ENV=test vitest --no-watch", "test:watch": "NODE_ENV=test vitest --watch", "test:run": "NODE_ENV=test vitest run", "test:ui": "NODE_ENV=test vitest --ui", "test:coverage": "NODE_ENV=test vitest run --coverage"}, "keywords": ["typescript", "prisma", "batch", "aws", "ecs"], "author": "Equtum Team", "license": "MIT", "dependencies": {"@prisma/client": "^6.12.0", "dayjs": "^1.11.13", "dayjs-plugin-utc": "^0.1.2", "dotenv": "^16.3.1", "neverthrow": "^8.2.0", "uuid": "^11.1.0"}, "devDependencies": {"@biomejs/biome": "^2.1.2", "@quramy/jest-prisma": "^1.8.1", "@quramy/prisma-fabbrica": "^2.3.0", "@types/node": "^20.10.0", "@vitest/ui": "^3.2.4", "nodemon": "^3.0.2", "prisma": "^6.12.0", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "tsx": "^4.20.3", "typescript": "^5.3.0", "vitest": "^3.2.4"}, "engines": {"node": ">=18.0.0"}, "prisma": {"schema": "./prisma"}}