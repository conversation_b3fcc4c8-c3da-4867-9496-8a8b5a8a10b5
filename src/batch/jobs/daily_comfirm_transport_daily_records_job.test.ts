import { HorseFactory } from '@test/factories/horse_factory';
import { HorseStatusFactory } from '@test/factories/horse_status_factory';
import { SectionFactory } from '@test/factories/section_factory';
import { StableFactory } from '@test/factories/stable_factory';
import { TransportDailyRecordFactory } from '@test/factories/stable_tm_transport_daily_record_factory';
import { TransportRecordFactory } from '@test/factories/stable_tm_transport_record_factory';
import { describe, expect, it, vi } from 'vitest';
import { dailyConfirmTransportDailyRecordsJob } from './daily_comfirm_transport_daily_records_job';

// Mock timezone utility
vi.mock('@/utils/timezone', () => ({
  getLocalTimeToUTCDate: vi.fn((dateString: string, timezone: string) => {
    // Mock implementation that returns a fixed date for testing
    return new Date('2024-12-31T00:10:00.000Z');
  }),
}));

describe('dailyConfirmTransportDailyRecordJob', () => {
  it('未確認のtransport daily recordsを確認済みにし、horse statusを更新する', async () => {
    // Arrange
    const stable = await StableFactory.create();
    const section = await SectionFactory.create({
      stable: { connect: { stableUuid: stable.stableUuid } },
    });
    const horse = await HorseFactory.create();

    // 未確認のtransport daily recordを作成
    const transportDailyRecord = await TransportDailyRecordFactory.create({
      section: { connect: { sectionId: section.sectionId } },
      stable: { connect: { stableUuid: stable.stableUuid } },
      year: 2024,
      month: 12,
      day: 30,
      isConfirmed: false,
    });

    // transport recordを作成（入厩）
    await TransportRecordFactory.create({
      transportDailyRecord: {
        connect: { transportDailyRecordId: transportDailyRecord.transportDailyRecordId },
      },
      horse: { connect: { horseId: horse.horseId } },
      type: 'in',
    });

    // 既存のhorse statusを作成
    await HorseStatusFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      inStable: false,
    });

    // Act
    await dailyConfirmTransportDailyRecordsJob();

    // Assert
    // transport daily recordが確認済みになっているかチェック
    const updatedRecord = await vPrisma.client.stableTmTransportDailyRecord.findUnique({
      where: { transportDailyRecordId: transportDailyRecord.transportDailyRecordId },
    });
    expect(updatedRecord?.isConfirmed).toBe(true);

    // horse statusが更新されているかチェック
    const updatedHorseStatus = await vPrisma.client.horseStatus.findUnique({
      where: { horseId: horse.horseId },
    });
    expect(updatedHorseStatus?.inStable).toBe(true);
    expect(updatedHorseStatus?.latestStableInDate).toEqual(new Date(2024, 11, 30)); // month is 0-indexed
  });

  it('未確認レコードがない場合は何も処理しない', async () => {
    // Arrange
    // 確認済みのレコードのみ作成
    const stable = await StableFactory.create();
    const section = await SectionFactory.create({
      stable: { connect: { stableUuid: stable.stableUuid } },
    });

    await TransportDailyRecordFactory.create({
      section: { connect: { sectionId: section.sectionId } },
      stable: { connect: { stableUuid: stable.stableUuid } },
      year: 2024,
      month: 12,
      day: 30,
      isConfirmed: true, // 既に確認済み
    });

    // Act & Assert
    // エラーが発生しないことを確認
    await expect(dailyConfirmTransportDailyRecordsJob()).resolves.not.toThrow();
  });

  it('出厩レコードの場合はinStableをfalseに設定する', async () => {
    // Arrange
    const stable = await StableFactory.create();
    const section = await SectionFactory.create({
      stable: { connect: { stableUuid: stable.stableUuid } },
    });
    const horse = await HorseFactory.create();

    const transportDailyRecord = await TransportDailyRecordFactory.create({
      section: { connect: { sectionId: section.sectionId } },
      stable: { connect: { stableUuid: stable.stableUuid } },
      year: 2024,
      month: 12,
      day: 30,
      isConfirmed: false,
    });

    // transport recordを作成（出厩）
    await TransportRecordFactory.create({
      transportDailyRecord: {
        connect: { transportDailyRecordId: transportDailyRecord.transportDailyRecordId },
      },
      horse: { connect: { horseId: horse.horseId } },
      type: 'out',
    });

    await HorseStatusFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      inStable: true,
    });

    // Act
    await dailyConfirmTransportDailyRecordsJob();

    // Assert
    const updatedHorseStatus = await vPrisma.client.horseStatus.findUnique({
      where: { horseId: horse.horseId },
    });
    expect(updatedHorseStatus?.inStable).toBe(false);
    expect(updatedHorseStatus?.latestStableOutDate).toEqual(new Date(2024, 11, 30));
  });

  it('複数の馬のレコードを正しく処理する', async () => {
    // Arrange
    const stable = await StableFactory.create();
    const section = await SectionFactory.create({
      stable: { connect: { stableUuid: stable.stableUuid } },
    });
    const horse1 = await HorseFactory.create();
    const horse2 = await HorseFactory.create();

    const transportDailyRecord = await TransportDailyRecordFactory.create({
      section: { connect: { sectionId: section.sectionId } },
      stable: { connect: { stableUuid: stable.stableUuid } },
      year: 2024,
      month: 12,
      day: 30,
      isConfirmed: false,
    });

    // 馬1: 入厩
    await TransportRecordFactory.create({
      transportDailyRecord: {
        connect: { transportDailyRecordId: transportDailyRecord.transportDailyRecordId },
      },
      horse: { connect: { horseId: horse1.horseId } },
      type: 'in',
    });

    // 馬2: 出厩
    await TransportRecordFactory.create({
      transportDailyRecord: {
        connect: { transportDailyRecordId: transportDailyRecord.transportDailyRecordId },
      },
      horse: { connect: { horseId: horse2.horseId } },
      type: 'out',
    });

    await HorseStatusFactory.create({
      horse: { connect: { horseId: horse1.horseId } },
      inStable: false,
    });
    await HorseStatusFactory.create({
      horse: { connect: { horseId: horse2.horseId } },
      inStable: true,
    });

    // Act
    await dailyConfirmTransportDailyRecordsJob();

    // Assert
    const horse1Status = await vPrisma.client.horseStatus.findUnique({
      where: { horseId: horse1.horseId },
    });
    const horse2Status = await vPrisma.client.horseStatus.findUnique({
      where: { horseId: horse2.horseId },
    });

    expect(horse1Status?.inStable).toBe(true);
    expect(horse2Status?.inStable).toBe(false);
  });
});
